{"name": "@metamorphic-reactor/api", "version": "1.0.0", "type": "module", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "ws": "^8.14.2", "@supabase/supabase-js": "^2.38.4", "fast-json-patch": "^3.1.1", "zod": "^3.23.8"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/ws": "^8.5.10", "@types/jest": "^29.5.12", "@types/supertest": "^6.0.2", "jest": "^29.7.0", "supertest": "^6.3.4", "ts-jest": "^29.1.1", "tsx": "^4.7.0", "typescript": "^5.5.3"}}