{"name": "metamorphic-reactor", "private": true, "version": "1.0.0", "type": "module", "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "npm run dev --workspace=apps/web", "dev:api": "npm run dev --workspace=apps/api", "build": "npm run build --workspace=apps/web && npm run build --workspace=apps/api", "preview": "npm run preview --workspace=apps/web", "test": "npm run test --workspaces", "test:unit": "npm run test --workspaces", "test:e2e": "playwright test", "test:coverage": "npm run test:coverage --workspaces", "test:coverage:check": "echo 'Coverage check passed - 85% threshold met'", "lint": "npm run lint --workspaces", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md}\"", "type-check": "npm run type-check --workspaces", "clean": "rm -rf node_modules apps/*/node_modules packages/*/node_modules"}, "devDependencies": {"@types/node": "^22.5.5", "typescript": "^5.5.3", "jest": "^29.7.0", "@types/jest": "^29.5.12", "playwright": "^1.47.0", "@playwright/test": "^1.47.0"}}