import express from 'express';
import cors from 'cors';
import { WebSocketServer } from 'ws';
import { createServer } from 'http';
import { loopRouter } from './routes/loop.js';
import { secretsRouter } from './routes/secrets.js';
import { supabaseClient } from './services/supabase.js';
import { secretsService } from './services/secretsService.js';
import {
  helmetConfig,
  generalRateLimit,
  strictRateLimit,
  secretStrippingMiddleware,
  inputValidationMiddleware,
  securityLoggingMiddleware
} from './middleware/security.js';

const app = express();
const server = createServer(app);
const wss = new WebSocketServer({ server });

// Security middleware (must be first)
app.use(helmetConfig);
app.use(securityLoggingMiddleware);
app.use(generalRateLimit);

// CORS configuration
const corsOptions = {
  origin: process.env.CORS_ORIGIN || 'http://localhost:5173',
  credentials: true,
  optionsSuccessStatus: 200
};
app.use(cors(corsOptions));

// Body parsing with size limits
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Input validation and secret stripping
app.use(inputValidationMiddleware);
app.use(secretStrippingMiddleware);

// Routes with additional rate limiting for sensitive endpoints
app.use('/api/secrets', strictRateLimit); // Apply strict rate limiting to secrets endpoints
app.use('/api', loopRouter);
app.use('/api', secretsRouter);

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// WebSocket for real-time streaming
wss.on('connection', (ws) => {
  console.log('Client connected to WebSocket');
  
  ws.on('message', (message) => {
    try {
      const data = JSON.parse(message.toString());
      console.log('Received WebSocket message:', data);
      
      // Echo back for now - extend for real-time loop updates
      ws.send(JSON.stringify({
        type: 'echo',
        data,
        timestamp: new Date().toISOString()
      }));
    } catch (error) {
      console.error('WebSocket message error:', error);
      ws.send(JSON.stringify({
        type: 'error',
        message: 'Invalid message format',
        timestamp: new Date().toISOString()
      }));
    }
  });
  
  ws.on('close', () => {
    console.log('Client disconnected from WebSocket');
  });
});

const PORT = process.env.PORT || 3001;

server.listen(PORT, async () => {
  console.log(`🚀 Metamorphic Reactor API running on port ${PORT}`);
  console.log(`📡 WebSocket server ready for real-time streaming`);

  // Initialize secrets from environment variables
  try {
    await secretsService.initializeFromEnv();
    console.log('🔐 Secrets service initialized');
  } catch (error) {
    console.error('Failed to initialize secrets service:', error);
  }
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});
